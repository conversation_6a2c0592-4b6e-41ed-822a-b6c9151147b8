using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using webApi.Models;
using webApi.Services;

namespace webApi.Hubs
{
    /// <summary>
    /// هاب الإشعارات - يدير إرسال الإشعارات في الوقت الحقيقي للمستخدمين
    /// </summary>
    public class NotificationHub : Hub
    {
        private readonly TasksDbContext _context;
        private readonly ILogger<NotificationHub> _logger;
        private readonly IConnectionTrackingService _connectionTracking;

        public NotificationHub(TasksDbContext context, ILogger<NotificationHub> logger, IConnectionTrackingService connectionTracking)
        {
            _context = context;
            _logger = logger;
            _connectionTracking = connectionTracking;
        }

        /// <summary>
        /// عند قطع اتصال عميل
        /// </summary>
        /// <param name="exception">استثناء قطع الاتصال إن وجد</param>
        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            try
            {
                // إزالة الاتصال من خدمة التتبع
                await _connectionTracking.RemoveConnectionAsync(Context.ConnectionId);

                _logger.LogInformation("عميل منقطع من NotificationHub: {ConnectionId}, السبب: {Exception}",
                    Context.ConnectionId, exception?.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في معالجة انقطاع الاتصال: {ConnectionId}", Context.ConnectionId);
            }

            await base.OnDisconnectedAsync(exception);
        }

        /// <summary>
        /// انضمام المستخدم إلى مجموعة الإشعارات الخاصة به
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        public async Task<object> JoinUserNotificationGroup(string userId)
        {
            try
            {
                // التحقق من صحة معرف المستخدم
                if (string.IsNullOrWhiteSpace(userId))
                {
                    _logger.LogWarning("محاولة انضمام بمعرف مستخدم فارغ من {ConnectionId}", Context.ConnectionId);
                    await Clients.Caller.SendAsync("Error", new
                    {
                        Message = "معرف المستخدم مطلوب",
                        UserId = userId
                    });
                    return new { Success = false, Message = "معرف المستخدم مطلوب" };
                }

                // اسم المجموعة يكون بتنسيق User_{userId}
                var groupName = $"User_{userId}";
                await Groups.AddToGroupAsync(Context.ConnectionId, groupName);

                // إضافة الاتصال لخدمة التتبع
                await _connectionTracking.AddConnectionAsync(int.Parse(userId), Context.ConnectionId,
                    Context.GetHttpContext()?.Request.Headers["User-Agent"].ToString());

                // إشعار المستخدم بنجاح الانضمام
                await Clients.Caller.SendAsync("JoinedNotificationGroup", new
                {
                    UserId = userId,
                    GroupName = groupName,
                    JoinedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                });

                _logger.LogInformation("المستخدم {ConnectionId} انضم لمجموعة الإشعارات {UserId}",
                    Context.ConnectionId, userId);

                return new { Success = true, Message = "تم الانضمام بنجاح" };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في انضمام المستخدم لمجموعة الإشعارات {UserId}", userId);
                return new { Success = false, Message = "خطأ في الانضمام للمجموعة" };
            }
        }

        /// <summary>
        /// مغادرة المستخدم لمجموعة الإشعارات الخاصة به
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        public async Task<object> LeaveUserNotificationGroup(string userId)
        {
            try
            {
                var groupName = $"User_{userId}";
                await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);

                _logger.LogInformation("المستخدم {ConnectionId} غادر مجموعة الإشعارات {UserId}",
                    Context.ConnectionId, userId);

                return new { Success = true, Message = "تم المغادرة بنجاح" };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في مغادرة المستخدم لمجموعة الإشعارات {UserId}", userId);
                return new { Success = false, Message = "خطأ في مغادرة المجموعة" };
            }
        }

        /// <summary>
        /// إرسال إشعار لمستخدم محدد
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="notification">كائن الإشعار</param>
        public async Task<object> SendNotificationToUser(string userId, Notification notification)
        {
            try
            {
                // التحقق من صحة المعاملات
                if (string.IsNullOrWhiteSpace(userId))
                {
                    _logger.LogWarning("محاولة إرسال إشعار بمعرف مستخدم فارغ");
                    return new { Success = false, Message = "معرف المستخدم مطلوب" };
                }

                if (notification == null)
                {
                    _logger.LogWarning("محاولة إرسال إشعار فارغ للمستخدم {UserId}", userId);
                    return new { Success = false, Message = "بيانات الإشعار مطلوبة" };
                }

                // فحص إعدادات المستخدم لهذا النوع
                if (int.TryParse(userId, out int userIdInt))
                {
                    var setting = await _context.NotificationSettings.FirstOrDefaultAsync(ns =>
                        ns.UserId == userIdInt &&
                        ns.NotificationType == notification.Type &&
                        ns.IsEnabled &&
                        !ns.IsDeleted
                    );
                    if (setting == null)
                    {
                        _logger.LogInformation("تم منع إرسال إشعار للمستخدم {UserId} بسبب تعطيل الإعدادات لنوع {Type}", userId, notification.Type);
                        return new { Success = false, Message = "الإشعارات لهذا النوع معطلة للمستخدم" };
                    }
                }

                // اسم المجموعة يكون بتنسيق User_{userId}
                var groupName = $"User_{userId}";

                // إرسال الإشعار للمستخدم
                await Clients.Group(groupName).SendAsync("ReceiveNotification", notification);

                _logger.LogInformation("تم إرسال إشعار للمستخدم {UserId}", userId);
                return new { Success = true, Message = "تم إرسال الإشعار بنجاح" };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إرسال إشعار للمستخدم {UserId}", userId);
                return new { Success = false, Message = "خطأ في إرسال الإشعار" };
            }
        }

        /// <summary>
        /// إرسال إشعار لمجموعة من المستخدمين
        /// </summary>
        /// <param name="userIds">قائمة معرفات المستخدمين</param>
        /// <param name="notification">كائن الإشعار</param>
        public async Task<object> SendNotificationToUsers(List<string> userIds, Notification notification)
        {
            try
            {
                // التحقق من صحة المعاملات
                if (userIds == null || !userIds.Any())
                {
                    _logger.LogWarning("محاولة إرسال إشعار بقائمة مستخدمين فارغة");
                    return new { Success = false, Message = "قائمة المستخدمين مطلوبة" };
                }

                if (notification == null)
                {
                    _logger.LogWarning("محاولة إرسال إشعار فارغ لمجموعة مستخدمين");
                    return new { Success = false, Message = "بيانات الإشعار مطلوبة" };
                }

                // إرسال الإشعار لكل مستخدم في القائمة
                foreach (var userId in userIds)
                {
                    var groupName = $"User_{userId}";
                    await Clients.Group(groupName).SendAsync("ReceiveNotification", notification);
                }

                _logger.LogInformation("تم إرسال إشعار لـ {Count} مستخدم", userIds.Count);
                return new { Success = true, Message = "تم إرسال الإشعار بنجاح" };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إرسال إشعار لمجموعة مستخدمين");
                return new { Success = false, Message = "خطأ في إرسال الإشعار" };
            }
        }

        /// <summary>
        /// إرسال إشعار لجميع المستخدمين
        /// </summary>
        /// <param name="notification">كائن الإشعار</param>
        public async Task<object> SendNotificationToAll(Notification notification)
        {
            try
            {
                // التحقق من صحة المعاملات
                if (notification == null)
                {
                    _logger.LogWarning("محاولة إرسال إشعار فارغ لجميع المستخدمين");
                    return new { Success = false, Message = "بيانات الإشعار مطلوبة" };
                }

                // إرسال الإشعار لجميع المتصلين
                await Clients.All.SendAsync("ReceiveNotification", notification);

                _logger.LogInformation("تم إرسال إشعار لجميع المستخدمين");
                return new { Success = true, Message = "تم إرسال الإشعار بنجاح" };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إرسال إشعار لجميع المستخدمين");
                return new { Success = false, Message = "خطأ في إرسال الإشعار" };
            }
        }

        /// <summary>
        /// تحديث عدد الإشعارات غير المقروءة للمستخدم
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        public async Task<object> UpdateUnreadCount(string userId)
        {
            try
            {
                // التحقق من صحة معرف المستخدم
                if (string.IsNullOrWhiteSpace(userId) || !int.TryParse(userId, out int userIdInt))
                {
                    _logger.LogWarning("محاولة تحديث عدد الإشعارات بمعرف مستخدم غير صالح");
                    return new { Success = false, Message = "معرف المستخدم غير صالح" };
                }

                // الحصول على عدد الإشعارات غير المقروءة
                var unreadCount = await _context.Notifications
                    .CountAsync(n => n.UserId == userIdInt && !n.IsRead);

                // اسم المجموعة يكون بتنسيق User_{userId}
                var groupName = $"User_{userId}";

                // إرسال التحديث للمستخدم
                await Clients.Group(groupName).SendAsync("UnreadCountUpdated", new { Count = unreadCount });

                _logger.LogInformation("تم تحديث عدد الإشعارات غير المقروءة للمستخدم {UserId}: {Count}", userId, unreadCount);
                return new { Success = true, Message = "تم تحديث العدد بنجاح", Count = unreadCount };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث عدد الإشعارات غير المقروءة للمستخدم {UserId}", userId);
                return new { Success = false, Message = "خطأ في تحديث العدد" };
            }
        }
    }
}